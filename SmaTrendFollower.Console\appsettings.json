{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "Redis": {
    "ConnectionString": "localhost:6379",
    "UniverseCacheConnection": "localhost:6379"
  },
  "Brave": {
    "SearchApiKey": "",
    "AiApiKey": "",
    "RateLimitPerSecond": 1,
    "MaxRequestsPerMonth": 2000,
    "TimeoutSeconds": 30
  },
  "Universe": {
    "UsePolygon": true,
    "MarketCapMin": 5000000000,
    "AvgVolumeMin": 1000000,
    "MaxSymbols": 200
  },
  "UniverseCache": {
    "RefreshInterval": "00:10:00"
  },

  // Enable Polygon-based universe management
  "UsePolygonUniverse": true,

  // Polygon Symbol Universe Configuration
  "PolygonUniverse": {
    "PageSize": 1000,
    "MaxSymbols": 0,
    "IncludedMarkets": ["stocks"],
    "IncludedTypes": ["CS", "ETF"],
    "ActiveOnly": true,
    "DelayBetweenCalls": 200,
    "CacheTtlHours": 168
  },

  // Polygon Snapshot Service Configuration
  "PolygonSnapshot": {
    "BatchSize": 50,
    "MaxConcurrency": 5,
    "DelayBetweenCalls": 200
  },

  // Daily Universe Refresh Configuration
  "UniverseRefresh": {
    "RefreshTimeUtc": "12:30:00",
    "MinPrice": 10.0,
    "MinAverageVolume": 500000,
    "MinVolatilityPercent": 1.0,
    "MaxCandidates": 200,
    "AnalysisPeriodDays": 20,
    "MinMarketCap": null,
    "CacheTtlHours": 24,
    "ExcludedExchanges": [],
    "ExcludedTypes": []
  },

  // Enhanced DynamicUniverseProvider Configuration
  "DynamicUniverse": {
    "UsePolygonIntegration": true,
    "FallbackToStaticSymbols": true,
    "MaxConcurrentBatches": 3,
    "BatchSize": 20,
    "DefaultCriteria": {
      "MinPrice": 10.0,
      "MinAverageVolume": 500000,
      "MinVolatilityPercent": 1.0,
      "AnalysisPeriodDays": 20,
      "MaxSymbols": 200
    }
  },

  "ML": {
    "PositionModelPath": "Model/position_model.zip"
  },
  "SlippageTraining": {
    "ModelOutputPath": "Model/slippage_model.zip"
  },
  "VWAP": {
    "StartHour": 9,
    "StartMinute": 40
  },

  // === Enhanced Services Configuration ===
  "EnhancedServices": {
    "EnableEnhancedDataRetrieval": true,
    "EnableAdaptiveRateLimit": true,
    "EnableAdaptiveSignalGeneration": true,
    "EnableEnhancedMetrics": true,
    "EnableSyntheticData": true,
    "EnableEmergencyMode": true
  },

  // Enhanced Data Retrieval Configuration
  "EnhancedDataRetrieval": {
    "MaxConcurrentRequests": 20,
    "PrimaryApiTimeout": "00:00:45",
    "BatchTimeout": "00:03:00",
    "RelaxedStalenessThreshold": "02:00:00",
    "EmergencyModeMaxStaleness": "1.00:00:00",
    "EmergencyModeTimeout": "00:15:00",
    "EnableSyntheticData": true,
    "MinimumBatchSuccessRate": 0.7,
    "MaxFailedAttempts": 3
  },

  // Synthetic Data Generation Configuration
  "SyntheticData": {
    "RandomSeed": null,
    "DefaultStartPrice": 100.0,
    "VolumeMultiplier": 0.5,
    "MaxCorrelation": 0.95,
    "MinCorrelation": 0.1,
    "DefaultVolatility": 0.02,
    "UseSectorCorrelations": true
  },

  // Flexible Data Staleness Configuration
  "FlexibleStaleness": {
    "MarketHours": {
      "HistoricalBars": "00:18:00",
      "RealTimeQuotes": "00:02:00",
      "IndexData": "00:15:00",
      "VixData": "00:15:00"
    },
    "AfterHours": {
      "HistoricalBars": "08:00:00",
      "RealTimeQuotes": "08:00:00",
      "IndexData": "08:00:00",
      "VixData": "08:00:00"
    },
    "EmergencyModeDuration": "00:15:00",
    "AllowCriticalOverrides": true,
    "AllowFallbackDataUsage": true,
    "LogStalenessWarnings": true
  },

  // Adaptive Rate Limiting Configuration
  "AdaptiveRateLimit": {
    "Providers": {
      "Alpaca": {
        "InitialLimit": 50,
        "MinLimit": 10,
        "MaxLimit": 100,
        "CircuitBreakerConfig": {
          "FailureThreshold": 5,
          "OpenTimeout": "00:02:00",
          "SuccessThreshold": 1
        }
      },
      "Polygon": {
        "InitialLimit": 80,
        "MinLimit": 20,
        "MaxLimit": 150,
        "CircuitBreakerConfig": {
          "FailureThreshold": 3,
          "OpenTimeout": "00:01:00",
          "SuccessThreshold": 1
        }
      }
    },
    "AdjustmentInterval": "00:02:00",
    "AdjustmentWindow": "00:05:00",
    "SuccessRateThreshold": 0.95,
    "AcquisitionTimeout": "00:00:30"
  },

  // Adaptive Signal Generation Configuration
  "AdaptiveSignal": {
    "MaxSymbolsToProcess": 500,
    "MaxConcurrentSymbols": 20,
    "MaxLookbackDays": 300,
    "MinimumBarsRequired": 10,
    "MinimumConfidenceScore": 0.5,
    "EnableSyntheticData": true,
    "EnableFallbackStrategies": true,
    "StrategyPreferences": {
      "FullSMAMinBars": 200,
      "AdaptedSMAMinBars": 100,
      "MomentumBasedMinBars": 50,
      "ShortTermTrendMinBars": 20,
      "PriceActionMinBars": 10,
      "ConfidenceMultipliers": {
        "FullSMA": 1.0,
        "AdaptedSMA": 0.9,
        "MomentumBased": 0.8,
        "ShortTermTrend": 0.7,
        "PriceAction": 0.6
      }
    }
  },

  // Robust Signal Generation Configuration
  "RobustSignal": {
    "MaxConcurrentGenerations": 5,
    "MinimumAcceptableSignals": 3,
    "MinimumConfidenceScore": 0.5,
    "EnableFallbackGeneration": true,
    "EnableEmergencyGeneration": true,
    "AdaptiveGenerationTimeout": "00:02:00",
    "FallbackGenerationTimeout": "00:01:00",
    "EmergencyModeDuration": "00:10:00",
    "MaxErrorsPerSymbol": 5,
    "ErrorCooldownPeriod": "00:30:00",
    "CoreSymbols": ["SPY", "QQQ", "IWM", "VTI", "VOO", "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA"],
    "RetryConfig": {
      "MaxRetries": 3,
      "BaseDelay": "00:00:01",
      "MaxDelay": "00:00:30",
      "UseExponentialBackoff": true,
      "UseJitter": true
    }
  },

  "AllowedHosts": "*"
}
