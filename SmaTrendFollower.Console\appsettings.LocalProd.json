{
  "Redis": {
    "ConnectionString": "192.168.1.168:6379",
    "UniverseCacheConnection": "192.168.1.168:6379"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "SmaTrendFollower": "Information"
    }
  },
  "Alpaca": {
    "KeyId": "AKGBPW5HD8LVI5C6NJUJ",
    "SecretKey": "MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM",
    "Environment": "live"
  },
  "Polygon": {
    "ApiKey": "********************************"
  },
  "Discord": {
    "BotToken": "MTM4NTA1OTI3MDMzNjMxNTQ1NA.GlZAu0.dZaZAZdW5ivUiXDBDE6yqoPI-HdGA9uh2kX8qo",
    "ChannelId": "1385057459814797383"
  },
  "Brave": {
    "SearchApiKey": "BSAzbdfJM7ozNY6D6D9Cs_OccWRoRWz",
    "AiApiKey": "BSAacxwbtDjQfP4151QopodZgaSS8jS",
    "RateLimitPerSecond": 1,
    "MaxRequestsPerMonth": 2000,
    "TimeoutSeconds": 30
  },
  "Safety": {
    "AllowedEnvironment": "Live",
    "MaxDailyLoss": 500,
    "MaxPositions": 8,
    "MaxSingleTradeValue": 3000,
    "MinAccountEquity": 5000,
    "MaxPositionSizePercent": 0.12,
    "MaxDailyTrades": 25,
    "RequireConfirmation": false,
    "DryRunMode": false
  },
  "Strategy": {
    "UniverseSize": 500,
    "TopNSymbols": 5,
    "VixThreshold": 25.0,
    "EnableRegimeFilter": true,
    "EnableVolatilityFilter": true
  },
  "Options": {
    "EnableOptionsOverlay": false,
    "EnableProtectivePuts": false,
    "EnableCoveredCalls": false
  },
  "WheelStrategy": {
    "Enabled": true,
    "MaxAllocationPercent": 0.20,
    "MinPremiumPercent": 0.01,
    "MinDaysToExpiration": 7,
    "MaxDaysToExpiration": 45,
    "MaxDeltaForPuts": 0.30,
    "MaxDeltaForCalls": 0.30,
    "MinLiquidity": 100,
    "MaxBidAskSpreadPercent": 0.05,
    "EnableRolling": true,
    "RollThreshold": 0.50,
    "MaxRollAttempts": 2,
    "RequireHighIV": true,
    "MinIVPercentile": 30,
    "AllowedSymbols": ["SPY", "QQQ", "AAPL", "MSFT", "TSLA", "NVDA", "AMD", "AMZN"],
    "ExcludedSymbols": [],
    "Timing": {
      "EntryWindowStart": "09:35:00",
      "EntryWindowEnd": "15:30:00",
      "CycleInterval": "00:15:00",
      "EnableExtendedHours": false,
      "EnablePreMarketEntry": false
    },
    "Risk": {
      "MaxDrawdownPercent": 0.10,
      "MaxDailyLossPercent": 0.05,
      "MaxActivePositions": 10,
      "MaxSinglePositionPercent": 0.05,
      "EnableEmergencyStop": true,
      "EmergencyStopThreshold": 0.15,
      "EnablePositionSizing": true,
      "VolatilityAdjustmentFactor": 1.0
    }
  },
  "UsePolygonUniverse": true,
  "PolygonUniverse": {
    "PageSize": 1000,
    "MaxSymbols": 0,
    "IncludedMarkets": ["stocks"],
    "IncludedTypes": ["CS", "ETF"],
    "ActiveOnly": true,
    "DelayBetweenCalls": 200,
    "CacheTtlHours": 168
  },
  "PolygonSnapshot": {
    "BatchSize": 50,
    "MaxConcurrency": 5,
    "DelayBetweenCalls": 200
  },
  "UniverseRefresh": {
    "RefreshTimeUtc": "12:30:00",
    "MinPrice": 10.0,
    "MinAverageVolume": 500000,
    "MinVolatilityPercent": 1.0,
    "MaxCandidates": 200,
    "AnalysisPeriodDays": 20,
    "MinMarketCap": null,
    "CacheTtlHours": 24,
    "ExcludedExchanges": [],
    "ExcludedTypes": []
  },
  "SubscriptionManager": {
    "SubscriptionTimeUtc": "13:25:00",
    "MaxSubscriptions": 200,
    "BatchSize": 50,
    "DelayBetweenBatches": 1000
  },
  "DynamicUniverse": {
    "UsePolygonIntegration": true,
    "FallbackToStaticSymbols": true,
    "MaxConcurrentBatches": 3,
    "BatchSize": 20,
    "DefaultCriteria": {
      "MinPrice": 10.0,
      "MinAverageVolume": 500000,
      "MinVolatilityPercent": 1.0,
      "AnalysisPeriodDays": 20,
      "MaxSymbols": 200
    }
  },
  "UniverseCache": {
    "RefreshInterval": "00:10:00"
  },

  // === Enhanced Services Configuration (Production Settings) ===
  "EnhancedServices": {
    "EnableEnhancedDataRetrieval": true,
    "EnableAdaptiveRateLimit": true,
    "EnableAdaptiveSignalGeneration": true,
    "EnableEnhancedMetrics": true,
    "EnableSyntheticData": false,
    "EnableEmergencyMode": true
  },

  // Enhanced Data Retrieval Configuration (Production)
  "EnhancedDataRetrieval": {
    "MaxConcurrentRequests": 30,
    "PrimaryApiTimeout": "00:01:00",
    "BatchTimeout": "00:05:00",
    "RelaxedStalenessThreshold": "01:00:00",
    "EmergencyModeMaxStaleness": "04:00:00",
    "EmergencyModeTimeout": "00:10:00",
    "EnableSyntheticData": false,
    "MinimumBatchSuccessRate": 0.8,
    "MaxFailedAttempts": 2
  },

  // Adaptive Rate Limiting Configuration (Production)
  "AdaptiveRateLimit": {
    "Providers": {
      "Alpaca": {
        "InitialLimit": 80,
        "MinLimit": 20,
        "MaxLimit": 150,
        "CircuitBreakerConfig": {
          "FailureThreshold": 3,
          "OpenTimeout": "00:01:30",
          "SuccessThreshold": 1
        }
      },
      "Polygon": {
        "InitialLimit": 100,
        "MinLimit": 30,
        "MaxLimit": 200,
        "CircuitBreakerConfig": {
          "FailureThreshold": 2,
          "OpenTimeout": "00:01:00",
          "SuccessThreshold": 1
        }
      }
    },
    "AdjustmentInterval": "00:01:30",
    "AdjustmentWindow": "00:03:00",
    "SuccessRateThreshold": 0.98,
    "AcquisitionTimeout": "00:00:20"
  },

  // Robust Signal Generation Configuration (Production)
  "RobustSignal": {
    "MaxConcurrentGenerations": 8,
    "MinimumAcceptableSignals": 5,
    "MinimumConfidenceScore": 0.6,
    "EnableFallbackGeneration": true,
    "EnableEmergencyGeneration": true,
    "AdaptiveGenerationTimeout": "00:01:30",
    "FallbackGenerationTimeout": "00:00:45",
    "EmergencyModeDuration": "00:05:00",
    "MaxErrorsPerSymbol": 3,
    "ErrorCooldownPeriod": "00:15:00"
  }
}
