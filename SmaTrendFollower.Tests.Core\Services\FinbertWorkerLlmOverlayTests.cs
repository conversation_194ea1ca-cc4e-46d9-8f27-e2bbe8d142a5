using FluentAssertions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using StackExchange.Redis;
using System.Threading.Channels;

namespace SmaTrendFollower.Tests.Core.Services;

public class FinbertWorkerLlmOverlayTests
{
    [Fact]
    public void FinbertWorker_WithLlmOverlay_CanBeConstructed()
    {
        // Arrange
        var httpClientFactory = Substitute.For<IHttpClientFactory>();
        var redisService = Substitute.For<IOptimizedRedisConnectionService>();
        var redisDatabase = Substitute.For<IDatabase>();
        var llmService = Substitute.For<LlmSentimentService>(
            Substitute.For<IHttpClientFactory>(),
            Substitute.For<IOptions<LlmSentimentOptions>>(),
            Substitute.For<ILogger<LlmSentimentService>>());
        var logger = Substitute.For<ILogger<FinbertWorker>>();
        var channel = Channel.CreateUnbounded<HeadlineItem>();

        var finbertOptions = Options.Create(new FinbertOptions
        {
            BaseUrl = "http://localhost:5000/predict",
            Parallelism = 2,
            TtlDays = 3
        });

        var llmOptions = Options.Create(new LlmSentimentOptions
        {
            Provider = "OpenAI",
            Model = "gpt-4o-mini",
            BlendWeight = 0.30,
            ConfidenceCutoff = 0.25,
            TimeoutSeconds = 10
        });

        redisService.GetDatabaseAsync().Returns(redisDatabase);

        // Act & Assert
        var worker = new FinbertWorker(
            channel,
            httpClientFactory,
            redisService,
            finbertOptions,
            llmService,
            llmOptions,
            logger);

        worker.Should().NotBeNull();
    }

    [Fact]
    public void LlmSentimentOptions_BlendingLogic_IsCorrect()
    {
        // Arrange
        var options = new LlmSentimentOptions
        {
            BlendWeight = 0.30,
            ConfidenceCutoff = 0.25
        };

        var finbertScore = 0.2; // Low confidence
        var llmScore = 0.6;     // High confidence

        // Act - simulate the blending formula from FinbertWorker
        var blendedScore = (1.0 - options.BlendWeight) * finbertScore + options.BlendWeight * llmScore;

        // Assert
        var expectedScore = 0.7 * 0.2 + 0.3 * 0.6; // = 0.14 + 0.18 = 0.32
        blendedScore.Should().BeApproximately(expectedScore, 0.001);
        Math.Abs(finbertScore).Should().BeLessThan(options.ConfidenceCutoff); // Should trigger LLM
    }
}
